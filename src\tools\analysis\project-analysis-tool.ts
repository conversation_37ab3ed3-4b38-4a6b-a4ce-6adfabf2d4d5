import { <PERSON>gger } from 'winston';
import { z } from 'zod';
import { Tool, <PERSON>l<PERSON><PERSON>ult, AgentContext } from '@/types';
import { ProjectAnalyzer } from '@core/context/project-analyzer';
import { EnvironmentDetector } from '@core/context/environment-detector';

const ProjectAnalysisToolSchema = z.object({
  directory: z.string().optional(),
  includeGitInfo: z.boolean().default(true),
  includeEnvironment: z.boolean().default(true),
  includeDependencies: z.boolean().default(true),
  includeFileStructure: z.boolean().default(true),
  maxDepth: z.number().min(1).max(10).default(5),
  analyzeCodeQuality: z.boolean().default(false),
});

type ProjectAnalysisToolParams = z.infer<typeof ProjectAnalysisToolSchema>;

export class ProjectAnalysisTool implements Tool {
  public readonly name = 'analyze_project';
  public readonly description = 'Analyze project structure, dependencies, git info, and environment details';
  public readonly parameters = ProjectAnalysisToolSchema;
  public readonly parallel = false; // Project analysis should be sequential
  public readonly dangerous = false;

  private readonly logger: Logger;
  private readonly projectAnalyzer: ProjectAnalyzer;
  private readonly environmentDetector: EnvironmentDetector;

  constructor(logger: Logger) {
    this.logger = logger;
    this.projectAnalyzer = new ProjectAnalyzer(logger);
    this.environmentDetector = new EnvironmentDetector(logger);
  }

  async execute(params: ProjectAnalysisToolParams, context: AgentContext): Promise<ToolResult> {
    const analysisDir = params.directory || context.workingDirectory;
    
    this.logger.info('Analyzing project', { 
      directory: analysisDir,
      includeGit: params.includeGitInfo,
      includeEnv: params.includeEnvironment 
    });

    try {
      const analysis: any = {
        directory: analysisDir,
        timestamp: new Date().toISOString(),
      };

      // Analyze project structure
      if (params.includeFileStructure) {
        this.logger.debug('Analyzing project structure');
        const projectStructure = await this.projectAnalyzer.analyzeProject(analysisDir);
        analysis.structure = {
          root: projectStructure.root,
          fileCount: projectStructure.files.length,
          directoryCount: projectStructure.directories.length,
          files: projectStructure.files.slice(0, 50), // Limit for performance
          directories: projectStructure.directories.slice(0, 20),
          gitInfo: projectStructure.gitInfo,
          packageInfo: projectStructure.packageInfo,
        };
      }

      // Analyze environment
      if (params.includeEnvironment) {
        this.logger.debug('Analyzing environment');
        const environment = await this.environmentDetector.detectEnvironment(analysisDir);
        const runtimeVersions = await this.environmentDetector.detectRuntimeVersions();
        const systemInfo = await this.environmentDetector.detectSystemInfo();
        
        analysis.environment = {
          ...environment,
          runtimeVersions,
          systemInfo,
        };
      }

      // Analyze dependencies
      if (params.includeDependencies && analysis.structure?.packageInfo) {
        this.logger.debug('Analyzing dependencies');
        analysis.dependencies = await this.analyzeDependencies(analysis.structure.packageInfo);
      }

      // Analyze code quality (basic metrics)
      if (params.analyzeCodeQuality && analysis.structure) {
        this.logger.debug('Analyzing code quality');
        analysis.codeQuality = await this.analyzeCodeQuality(analysis.structure.files);
      }

      // Generate insights
      analysis.insights = this.generateInsights(analysis);

      return {
        success: true,
        data: analysis,
        metadata: {
          analysisTime: Date.now(),
          directory: analysisDir,
          depth: params.maxDepth,
        },
      };
    } catch (error) {
      this.logger.error('Project analysis failed', { error, directory: analysisDir });

      return {
        success: false,
        error: (error as Error).message,
        metadata: {
          directory: analysisDir,
        },
      };
    }
  }

  private async analyzeDependencies(packageInfo: any): Promise<any> {
    const dependencies = packageInfo.dependencies || {};
    const analysis = {
      total: Object.keys(dependencies).length,
      byType: {} as Record<string, number>,
      outdated: [] as string[],
      security: [] as string[],
      popular: [] as string[],
    };

    // Categorize dependencies
    for (const [name, version] of Object.entries(dependencies)) {
      const category = this.categorizeDependency(name);
      analysis.byType[category] = (analysis.byType[category] || 0) + 1;

      // Check for common security issues (basic check)
      if (this.isKnownVulnerableDependency(name, version as string)) {
        analysis.security.push(name);
      }

      // Check for popular packages
      if (this.isPopularDependency(name)) {
        analysis.popular.push(name);
      }
    }

    return analysis;
  }

  private categorizeDependency(name: string): string {
    const categories = {
      'testing': ['jest', 'mocha', 'chai', 'cypress', 'playwright', 'vitest'],
      'build': ['webpack', 'vite', 'rollup', 'parcel', 'esbuild'],
      'linting': ['eslint', 'prettier', 'tslint'],
      'types': ['@types/', 'typescript'],
      'ui': ['react', 'vue', 'angular', 'svelte'],
      'backend': ['express', 'fastify', 'koa', 'nest'],
      'database': ['mongoose', 'prisma', 'typeorm', 'sequelize'],
      'utility': ['lodash', 'ramda', 'date-fns', 'moment'],
    };

    for (const [category, packages] of Object.entries(categories)) {
      if (packages.some(pkg => name.includes(pkg))) {
        return category;
      }
    }

    return 'other';
  }

  private isKnownVulnerableDependency(name: string, version: string): boolean {
    // Basic check for known vulnerable packages
    const knownVulnerable = [
      'event-stream',
      'flatmap-stream',
      'getcookies',
    ];

    return knownVulnerable.includes(name);
  }

  private isPopularDependency(name: string): boolean {
    const popular = [
      'react', 'vue', 'angular', 'express', 'lodash', 'axios', 'moment',
      'typescript', 'webpack', 'jest', 'eslint', 'prettier'
    ];

    return popular.some(pkg => name.includes(pkg));
  }

  private async analyzeCodeQuality(files: any[]): Promise<any> {
    const codeFiles = files.filter(file => 
      ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.go', '.rs'].includes(file.extension)
    );

    const analysis = {
      totalFiles: codeFiles.length,
      totalLines: 0,
      averageFileSize: 0,
      largestFiles: [] as any[],
      filesByLanguage: {} as Record<string, number>,
      complexity: {
        simple: 0,
        moderate: 0,
        complex: 0,
      },
    };

    for (const file of codeFiles) {
      const language = this.getLanguageFromExtension(file.extension);
      analysis.filesByLanguage[language] = (analysis.filesByLanguage[language] || 0) + 1;

      if (file.content) {
        const lines = file.content.split('\n').length;
        analysis.totalLines += lines;

        // Simple complexity analysis
        const complexity = this.calculateComplexity(file.content);
        if (complexity < 10) analysis.complexity.simple++;
        else if (complexity < 20) analysis.complexity.moderate++;
        else analysis.complexity.complex++;
      }
    }

    analysis.averageFileSize = analysis.totalLines / Math.max(analysis.totalFiles, 1);
    analysis.largestFiles = codeFiles
      .filter(f => f.content)
      .sort((a, b) => b.content.split('\n').length - a.content.split('\n').length)
      .slice(0, 5)
      .map(f => ({
        path: f.path,
        lines: f.content.split('\n').length,
      }));

    return analysis;
  }

  private getLanguageFromExtension(ext: string): string {
    const languageMap: Record<string, string> = {
      '.js': 'JavaScript',
      '.ts': 'TypeScript',
      '.jsx': 'React',
      '.tsx': 'React TypeScript',
      '.py': 'Python',
      '.java': 'Java',
      '.go': 'Go',
      '.rs': 'Rust',
      '.cpp': 'C++',
      '.c': 'C',
      '.cs': 'C#',
      '.php': 'PHP',
      '.rb': 'Ruby',
    };

    return languageMap[ext] || 'Other';
  }

  private calculateComplexity(content: string): number {
    // Simple cyclomatic complexity approximation
    const complexityKeywords = [
      'if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'try',
      '&&', '||', '?', ':', 'def', 'function', 'class'
    ];

    let complexity = 1; // Base complexity
    
    for (const keyword of complexityKeywords) {
      const matches = content.match(new RegExp(`\\b${keyword}\\b`, 'g'));
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  private generateInsights(analysis: any): string[] {
    const insights: string[] = [];

    // File structure insights
    if (analysis.structure) {
      const { fileCount, directoryCount } = analysis.structure;
      
      if (fileCount > 1000) {
        insights.push('Large project with over 1000 files - consider modularization');
      }
      
      if (directoryCount < 5 && fileCount > 50) {
        insights.push('Flat directory structure - consider organizing files into subdirectories');
      }

      if (analysis.structure.packageInfo) {
        const packageType = analysis.structure.packageInfo.type;
        insights.push(`Detected ${packageType} project`);
      }
    }

    // Environment insights
    if (analysis.environment) {
      const { platform, nodeVersion } = analysis.environment;
      insights.push(`Running on ${platform} with Node.js ${nodeVersion}`);

      if (analysis.environment.runtimeVersions.docker !== 'not installed') {
        insights.push('Docker is available for containerization');
      }

      if (analysis.environment.runtimeVersions.git !== 'not installed') {
        insights.push('Git is available for version control');
      }
    }

    // Dependencies insights
    if (analysis.dependencies) {
      const { total, security } = analysis.dependencies;
      
      if (total > 100) {
        insights.push('High number of dependencies - consider dependency audit');
      }
      
      if (security.length > 0) {
        insights.push(`Found ${security.length} potentially vulnerable dependencies`);
      }
    }

    // Code quality insights
    if (analysis.codeQuality) {
      const { totalFiles, complexity } = analysis.codeQuality;
      
      if (complexity.complex > totalFiles * 0.2) {
        insights.push('High complexity detected - consider refactoring');
      }
      
      if (analysis.codeQuality.averageFileSize > 500) {
        insights.push('Large average file size - consider breaking down files');
      }
    }

    return insights;
  }
}
