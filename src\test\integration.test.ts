import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { Logger } from 'winston';
import { ConfigManager } from '../config/config-manager';
import { ContextManager } from '../core/context/context-manager';
import { LLMProviderManager } from '../core/providers/llm-provider-manager';
import { ToolRegistry } from '../tools/tool-registry';
import { AutonomousAgent } from '../core/agent/autonomous-agent';
import { SessionManager } from '../core/session/session-manager';
import { ErrorRecoveryManager } from '../core/recovery/error-recovery';
import { AuditLogger } from '../core/logging/audit-logger';
import { TerminalUI } from '../ui/terminal-ui';
import { MemoryManager } from '../core/context/memory-manager';
import { BackupTool } from '../tools/filesystem/backup-tool';
import { CodeAnalysisTool } from '../tools/analysis/code-analysis-tool';

// Mock logger for testing
const createMockLogger = (): Logger => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  silly: jest.fn(),
  log: jest.fn(),
  level: 'info',
  levels: {},
  transports: [],
  format: {},
  exitOnError: false,
  silent: false,
  exceptions: {} as any,
  rejections: {} as any,
  profiler: {} as any,
  stream: {} as any,
  query: jest.fn(),
  add: jest.fn(),
  remove: jest.fn(),
  clear: jest.fn(),
  close: jest.fn(),
  configure: jest.fn(),
  child: jest.fn(),
  isLevelEnabled: jest.fn(),
  setMaxListeners: jest.fn(),
  getMaxListeners: jest.fn(),
  emit: jest.fn(),
  addListener: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  off: jest.fn(),
  removeAllListeners: jest.fn(),
  listeners: jest.fn(),
  rawListeners: jest.fn(),
  listenerCount: jest.fn(),
  prependListener: jest.fn(),
  prependOnceListener: jest.fn(),
  eventNames: jest.fn(),
} as any);

describe('Integration Tests', () => {
  let logger: Logger;
  let configManager: ConfigManager;
  let contextManager: ContextManager;
  let providerManager: LLMProviderManager;
  let toolRegistry: ToolRegistry;
  let autonomousAgent: AutonomousAgent;
  let sessionManager: SessionManager;
  let errorRecovery: ErrorRecoveryManager;
  let auditLogger: AuditLogger;
  let terminalUI: TerminalUI;

  beforeEach(async () => {
    logger = createMockLogger();
    configManager = new ConfigManager(logger);
    contextManager = new ContextManager(logger);
    providerManager = new LLMProviderManager(logger);
    toolRegistry = new ToolRegistry(logger);
    autonomousAgent = new AutonomousAgent(
      logger,
      providerManager,
      toolRegistry,
      contextManager
    );
    sessionManager = new SessionManager(logger);
    errorRecovery = new ErrorRecoveryManager(logger);
    auditLogger = new AuditLogger(logger);
    terminalUI = new TerminalUI(logger, configManager);
  });

  afterEach(async () => {
    await contextManager.shutdown();
    await sessionManager.shutdown();
    await errorRecovery.shutdown();
    await auditLogger.shutdown();
    terminalUI.shutdown();
  });

  describe('Core Components', () => {
    it('should initialize all core components without errors', () => {
      expect(configManager).toBeDefined();
      expect(contextManager).toBeDefined();
      expect(providerManager).toBeDefined();
      expect(toolRegistry).toBeDefined();
      expect(autonomousAgent).toBeDefined();
      expect(sessionManager).toBeDefined();
      expect(errorRecovery).toBeDefined();
      expect(auditLogger).toBeDefined();
      expect(terminalUI).toBeDefined();
    });

    it('should validate configuration schema', async () => {
      const validation = await configManager.validateConfig();
      expect(validation).toBeDefined();
      expect(typeof validation.valid).toBe('boolean');
    });

    it('should initialize context with working directory', async () => {
      const context = await contextManager.initializeContext('test-session');
      expect(context).toBeDefined();
      expect(context.workingDirectory).toBeDefined();
      expect(context.environment).toBeDefined();
      expect(context.projectStructure).toBeDefined();
    });

    it('should register tools in tool registry', () => {
      const tools = toolRegistry.getAvailableTools();
      expect(tools.length).toBeGreaterThan(0);
      
      // Check for specific tools
      const toolNames = tools.map(tool => tool.name);
      expect(toolNames).toContain('filesystem');
      expect(toolNames).toContain('shell');
      expect(toolNames).toContain('search');
      expect(toolNames).toContain('backup_manager');
      expect(toolNames).toContain('code_analysis');
    });
  });

  describe('Session Management', () => {
    it('should create and manage sessions', async () => {
      const context = await contextManager.initializeContext('test-session');
      const config = configManager.getConfig();
      
      const session = await sessionManager.createSession(
        'Test Session',
        context,
        config
      );
      
      expect(session).toBeDefined();
      expect(session.id).toBeDefined();
      expect(session.name).toBe('Test Session');
      expect(session.context).toBe(context);
      expect(session.config).toBe(config);
    });

    it('should list and retrieve sessions', async () => {
      const context = await contextManager.initializeContext('test-session');
      const config = configManager.getConfig();
      
      const session = await sessionManager.createSession(
        'Test Session',
        context,
        config
      );
      
      const sessions = await sessionManager.listSessions();
      expect(sessions).toContain(session);
      
      const retrievedSession = await sessionManager.getSession(session.id);
      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(session.id);
    });
  });

  describe('Memory Management', () => {
    it('should create and use memory manager', async () => {
      const memoryManager = new MemoryManager(logger, 'test-session');
      
      await memoryManager.addFact('test_key', 'test_value', 'test_context');
      await memoryManager.addLearning('Test learning', 'test_context');
      
      const memory = memoryManager.getMemory();
      expect(memory.facts['test_key']).toBe('test_value');
      expect(memory.learnings).toContain('Test learning');
      
      await memoryManager.shutdown();
    });

    it('should generate insights from patterns', async () => {
      const memoryManager = new MemoryManager(logger, 'test-session');
      
      // Add multiple similar facts to trigger pattern detection
      await memoryManager.addFact('file1', '/path/to/file1.js', 'file_analysis');
      await memoryManager.addFact('file2', '/path/to/file2.js', 'file_analysis');
      await memoryManager.addFact('file3', '/path/to/file3.js', 'file_analysis');
      
      const patterns = memoryManager.getPatterns();
      const insights = memoryManager.getInsights();
      
      expect(patterns).toBeDefined();
      expect(insights).toBeDefined();
      
      await memoryManager.shutdown();
    });
  });

  describe('Error Recovery', () => {
    it('should register and execute recovery strategies', async () => {
      const strategies = errorRecovery.getStrategies();
      expect(strategies.length).toBeGreaterThan(0);
      
      // Check for default strategies
      const strategyNames = strategies.map(s => s.name);
      expect(strategyNames).toContain('file_not_found_recovery');
      expect(strategyNames).toContain('permission_denied_recovery');
      expect(strategyNames).toContain('network_timeout_recovery');
    });

    it('should execute retry logic with exponential backoff', async () => {
      let attempts = 0;
      const operation = async () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Test error');
        }
        return 'success';
      };

      const result = await errorRecovery.executeWithRetry(
        operation,
        { maxAttempts: 3, baseDelay: 10 }
      );

      expect(result).toBe('success');
      expect(attempts).toBe(3);
    });
  });

  describe('Audit Logging', () => {
    it('should log and query audit events', async () => {
      await auditLogger.logEvent({
        type: 'task_started',
        severity: 'info',
        source: 'test',
        data: { test: 'data' },
      });

      const events = await auditLogger.queryEvents({
        type: ['task_started'],
        limit: 10,
      });

      expect(events.length).toBeGreaterThan(0);
      expect(events[0]!.type).toBe('task_started');
      expect(events[0]!.data.test).toBe('data');
    });

    it('should generate audit statistics', async () => {
      await auditLogger.logEvent({
        type: 'task_completed',
        severity: 'info',
        source: 'test',
        data: { duration: 1000 },
      });

      const stats = await auditLogger.getAuditStatistics();
      expect(stats).toBeDefined();
      expect(stats.totalEvents).toBeGreaterThan(0);
      expect(stats.eventsByType).toBeDefined();
      expect(stats.eventsBySeverity).toBeDefined();
    });
  });

  describe('Tool Integration', () => {
    it('should execute backup tool operations', async () => {
      const backupTool = new BackupTool(logger);
      const context = await contextManager.initializeContext('test-session');
      
      // Test listing backups (should work even with no backups)
      const listResult = await backupTool.execute(
        { operation: 'list' },
        context
      );
      
      expect(listResult.success).toBe(true);
      expect(listResult.data).toBeDefined();
    });

    it('should execute code analysis tool operations', async () => {
      const codeAnalysisTool = new CodeAnalysisTool(logger);
      const context = await contextManager.initializeContext('test-session');
      
      // Test analyzing a directory (should work even if directory doesn't exist)
      const analysisResult = await codeAnalysisTool.execute(
        {
          operation: 'analyze_directory',
          directoryPath: './src',
          analysisType: 'overview',
        },
        context
      );
      
      // Should either succeed or fail gracefully
      expect(typeof analysisResult.success).toBe('boolean');
    });
  });

  describe('Terminal UI', () => {
    it('should handle streaming output', () => {
      terminalUI.startStreaming();
      expect(terminalUI['streamingOutput']).toBe(true);
      
      terminalUI.stopStreaming();
      expect(terminalUI['streamingOutput']).toBe(false);
    });

    it('should manage spinners', () => {
      const stopSpinner = terminalUI.showSpinner('Test spinner', 'test-id');
      expect(terminalUI['activeSpinners'].has('test-id')).toBe(true);
      
      stopSpinner();
      expect(terminalUI['activeSpinners'].has('test-id')).toBe(false);
    });

    it('should format data correctly', () => {
      const fileSize = terminalUI.formatFileSize(1024);
      expect(fileSize).toBe('1.0 KB');
      
      const duration = terminalUI.formatDuration(5000);
      expect(duration).toBe('5.0s');
      
      const timestamp = terminalUI.formatTimestamp(new Date('2023-01-01'));
      expect(timestamp).toBeDefined();
    });
  });

  describe('Provider Health Check', () => {
    it('should perform health checks on providers', async () => {
      // This test would require actual provider configuration
      // For now, just test that the method exists and returns the right structure
      const healthResults = await providerManager.healthCheck();
      expect(healthResults).toBeDefined();
      expect(typeof healthResults).toBe('object');
    });
  });
});
