import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { z } from 'zod';
import { BaseTool, <PERSON><PERSON>R<PERSON>ult, AgentContext, ToolError } from '@/types';

const FileSystemToolSchema = z.discriminatedUnion('operation', [
  z.object({
    operation: z.literal('read'),
    path: z.string(),
    encoding: z.string().default('utf8'),
  }),
  z.object({
    operation: z.literal('write'),
    path: z.string(),
    content: z.string(),
    encoding: z.string().default('utf8'),
    createDirectories: z.boolean().default(true),
    backup: z.boolean().default(false),
  }),
  z.object({
    operation: z.literal('append'),
    path: z.string(),
    content: z.string(),
    encoding: z.string().default('utf8'),
  }),
  z.object({
    operation: z.literal('delete'),
    path: z.string(),
    recursive: z.boolean().default(false),
    backup: z.boolean().default(true),
  }),
  z.object({
    operation: z.literal('copy'),
    source: z.string(),
    destination: z.string(),
    overwrite: z.boolean().default(false),
  }),
  z.object({
    operation: z.literal('move'),
    source: z.string(),
    destination: z.string(),
    overwrite: z.boolean().default(false),
  }),
  z.object({
    operation: z.literal('mkdir'),
    path: z.string(),
    recursive: z.boolean().default(true),
  }),
  z.object({
    operation: z.literal('stat'),
    path: z.string(),
  }),
  z.object({
    operation: z.literal('chmod'),
    path: z.string(),
    mode: z.union([z.string(), z.number()]),
  }),
  z.object({
    operation: z.literal('exists'),
    path: z.string(),
  }),
]);

type FileSystemToolParams = z.infer<typeof FileSystemToolSchema>;

export class FileSystemTool extends BaseTool<FileSystemToolParams> {
  public readonly name = 'filesystem_operation';
  public readonly description = 'Perform comprehensive file system operations: read, write, create, delete, move, copy, chmod, and permissions management';
  public readonly parameters = FileSystemToolSchema;
  public readonly parallel = true;
  public readonly dangerous = true;

  private readonly logger: Logger;
  private readonly backupDir: string;

  constructor(logger: Logger) {
    this.logger = logger;
    this.backupDir = path.join(process.cwd(), '.agent-backups');
    this.ensureBackupDirectory();
  }

  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      this.logger.warn('Failed to create backup directory', { error });
    }
  }

  protected async executeTyped(params: FileSystemToolParams, context: AgentContext): Promise<ToolResult> {
    this.logger.info('Executing filesystem operation', { 
      operation: params.operation,
      path: 'path' in params ? params.path : undefined 
    });

    try {
      switch (params.operation) {
        case 'read':
          return await this.readFile(params, context);
        case 'write':
          return await this.writeFile(params, context);
        case 'append':
          return await this.appendFile(params, context);
        case 'delete':
          return await this.deleteFile(params, context);
        case 'copy':
          return await this.copyFile(params, context);
        case 'move':
          return await this.moveFile(params, context);
        case 'mkdir':
          return await this.createDirectory(params, context);
        case 'stat':
          return await this.getStats(params, context);
        case 'chmod':
          return await this.changePermissions(params, context);
        case 'exists':
          return await this.checkExists(params, context);
        default:
          throw new ToolError(`Unknown operation: ${(params as any).operation}`, this.name);
      }
    } catch (error) {
      this.logger.error('Filesystem operation failed', { 
        error, 
        operation: params.operation 
      });

      return {
        success: false,
        error: (error as Error).message,
        metadata: {
          operation: params.operation,
          path: 'path' in params ? params.path : undefined,
        },
      };
    }
  }

  private async readFile(
    params: Extract<FileSystemToolParams, { operation: 'read' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      const content = await fs.readFile(fullPath, params.encoding as BufferEncoding);
      const stats = await fs.stat(fullPath);

      return {
        success: true,
        data: {
          content,
          path: fullPath,
          size: stats.size,
          lastModified: stats.mtime,
          encoding: params.encoding,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to read file: ${(error as Error).message}`, this.name);
    }
  }

  private async writeFile(
    params: Extract<FileSystemToolParams, { operation: 'write' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      // Create backup if file exists and backup is requested
      if (params.backup && await this.fileExists(fullPath)) {
        await this.createBackup(fullPath);
      }

      // Create directories if needed
      if (params.createDirectories) {
        await fs.mkdir(path.dirname(fullPath), { recursive: true });
      }

      await fs.writeFile(fullPath, params.content, params.encoding as BufferEncoding);
      const stats = await fs.stat(fullPath);

      return {
        success: true,
        data: {
          path: fullPath,
          size: stats.size,
          created: true,
          backup: params.backup,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to write file: ${(error as Error).message}`, this.name);
    }
  }

  private async appendFile(
    params: Extract<FileSystemToolParams, { operation: 'append' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      await fs.appendFile(fullPath, params.content, params.encoding as BufferEncoding);
      const stats = await fs.stat(fullPath);

      return {
        success: true,
        data: {
          path: fullPath,
          size: stats.size,
          appended: true,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to append to file: ${(error as Error).message}`, this.name);
    }
  }

  private async deleteFile(
    params: Extract<FileSystemToolParams, { operation: 'delete' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      // Create backup if requested
      if (params.backup && await this.fileExists(fullPath)) {
        await this.createBackup(fullPath);
      }

      const stats = await fs.stat(fullPath);
      
      if (stats.isDirectory()) {
        if (params.recursive) {
          await fs.rmdir(fullPath, { recursive: true });
        } else {
          await fs.rmdir(fullPath);
        }
      } else {
        await fs.unlink(fullPath);
      }

      return {
        success: true,
        data: {
          path: fullPath,
          deleted: true,
          wasDirectory: stats.isDirectory(),
          backup: params.backup,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to delete: ${(error as Error).message}`, this.name);
    }
  }

  private async copyFile(
    params: Extract<FileSystemToolParams, { operation: 'copy' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const sourcePath = this.resolvePath(params.source, context.workingDirectory);
    const destPath = this.resolvePath(params.destination, context.workingDirectory);
    
    try {
      // Check if destination exists and overwrite is not allowed
      if (!params.overwrite && await this.fileExists(destPath)) {
        throw new Error('Destination exists and overwrite is false');
      }

      // Create destination directory if needed
      await fs.mkdir(path.dirname(destPath), { recursive: true });

      await fs.copyFile(sourcePath, destPath);
      const stats = await fs.stat(destPath);

      return {
        success: true,
        data: {
          source: sourcePath,
          destination: destPath,
          size: stats.size,
          copied: true,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to copy file: ${(error as Error).message}`, this.name);
    }
  }

  private async moveFile(
    params: Extract<FileSystemToolParams, { operation: 'move' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const sourcePath = this.resolvePath(params.source, context.workingDirectory);
    const destPath = this.resolvePath(params.destination, context.workingDirectory);
    
    try {
      // Check if destination exists and overwrite is not allowed
      if (!params.overwrite && await this.fileExists(destPath)) {
        throw new Error('Destination exists and overwrite is false');
      }

      // Create destination directory if needed
      await fs.mkdir(path.dirname(destPath), { recursive: true });

      await fs.rename(sourcePath, destPath);
      const stats = await fs.stat(destPath);

      return {
        success: true,
        data: {
          source: sourcePath,
          destination: destPath,
          size: stats.size,
          moved: true,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to move file: ${(error as Error).message}`, this.name);
    }
  }

  private async createDirectory(
    params: Extract<FileSystemToolParams, { operation: 'mkdir' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      await fs.mkdir(fullPath, { recursive: params.recursive });

      return {
        success: true,
        data: {
          path: fullPath,
          created: true,
          recursive: params.recursive,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to create directory: ${(error as Error).message}`, this.name);
    }
  }

  private async getStats(
    params: Extract<FileSystemToolParams, { operation: 'stat' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      const stats = await fs.stat(fullPath);

      return {
        success: true,
        data: {
          path: fullPath,
          size: stats.size,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          isSymbolicLink: stats.isSymbolicLink(),
          permissions: stats.mode.toString(8),
          lastModified: stats.mtime,
          lastAccessed: stats.atime,
          created: stats.birthtime,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to get stats: ${(error as Error).message}`, this.name);
    }
  }

  private async changePermissions(
    params: Extract<FileSystemToolParams, { operation: 'chmod' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      const mode = typeof params.mode === 'string' 
        ? parseInt(params.mode, 8) 
        : params.mode;

      await fs.chmod(fullPath, mode);

      return {
        success: true,
        data: {
          path: fullPath,
          mode: mode.toString(8),
          changed: true,
        },
      };
    } catch (error) {
      throw new ToolError(`Failed to change permissions: ${(error as Error).message}`, this.name);
    }
  }

  private async checkExists(
    params: Extract<FileSystemToolParams, { operation: 'exists' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    try {
      await fs.access(fullPath);
      return {
        success: true,
        data: {
          path: fullPath,
          exists: true,
        },
      };
    } catch {
      return {
        success: true,
        data: {
          path: fullPath,
          exists: false,
        },
      };
    }
  }

  private resolvePath(filePath: string, workingDirectory: string): string {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    return path.resolve(workingDirectory, filePath);
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private async createBackup(filePath: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `${path.basename(filePath)}.backup.${timestamp}`;
    const backupPath = path.join(this.backupDir, backupFileName);

    await fs.mkdir(this.backupDir, { recursive: true });
    await fs.copyFile(filePath, backupPath);

    this.logger.info('Created backup', { original: filePath, backup: backupPath });
    return backupPath;
  }

  async listBackups(): Promise<ToolResult> {
    try {
      const files = await fs.readdir(this.backupDir);
      const backups = files.filter(file => file.includes('.backup.'));

      const backupInfo = await Promise.all(
        backups.map(async (file) => {
          const filePath = path.join(this.backupDir, file);
          const stats = await fs.stat(filePath);
          return {
            name: file,
            path: filePath,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime,
          };
        })
      );

      return {
        success: true,
        data: {
          backups: backupInfo.sort((a, b) => b.created.getTime() - a.created.getTime()),
          totalBackups: backupInfo.length,
          backupDirectory: this.backupDir,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to list backups: ${(error as Error).message}`,
      };
    }
  }

  async restoreFromBackup(backupPath: string, targetPath: string): Promise<ToolResult> {
    try {
      if (!(await this.fileExists(backupPath))) {
        throw new Error(`Backup file not found: ${backupPath}`);
      }

      // Create backup of current file if it exists
      if (await this.fileExists(targetPath)) {
        await this.createBackup(targetPath);
      }

      await fs.copyFile(backupPath, targetPath);

      return {
        success: true,
        data: {
          restored: targetPath,
          fromBackup: backupPath,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to restore from backup: ${(error as Error).message}`,
      };
    }
  }

  async cleanupOldBackups(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<ToolResult> {
    try {
      const files = await fs.readdir(this.backupDir);
      const backups = files.filter(file => file.includes('.backup.'));
      const cutoffDate = new Date(Date.now() - maxAge);

      let deletedCount = 0;

      for (const backup of backups) {
        const filePath = path.join(this.backupDir, backup);
        const stats = await fs.stat(filePath);

        if (stats.birthtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
          this.logger.info('Deleted old backup', { backup: filePath });
        }
      }

      return {
        success: true,
        data: {
          deletedBackups: deletedCount,
          maxAge: maxAge,
          cutoffDate,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to cleanup old backups: ${(error as Error).message}`,
      };
    }
  }
}
