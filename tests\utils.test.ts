import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { promises as fs } from 'fs';
import path from 'path';
import {
  ensureDir,
  fileExists,
  isDirectory,
  sanitizeFilename,
  truncateString,
  capitalizeFirst,
  camelToKebab,
  kebabToCamel,
  chunk,
  unique,
  groupBy,
  deepClone,
  mergeDeep,
  formatDuration,
  formatBytes,
  isValidEmail,
  isValidUrl,
  generateId,
  sleep,
} from '../src/utils';

describe('File System Utilities', () => {
  const testDir = path.join(__dirname, 'temp');
  
  beforeEach(async () => {
    await ensureDir(testDir);
  });
  
  afterEach(async () => {
    try {
      await fs.rmdir(testDir, { recursive: true });
    } catch {
      // Ignore cleanup errors
    }
  });

  describe('ensureDir', () => {
    it('should create directory if it does not exist', async () => {
      const newDir = path.join(testDir, 'new-dir');
      await ensureDir(newDir);
      expect(await isDirectory(newDir)).toBe(true);
    });

    it('should not throw if directory already exists', async () => {
      await expect(ensureDir(testDir)).resolves.not.toThrow();
    });
  });

  describe('fileExists', () => {
    it('should return true for existing file', async () => {
      const testFile = path.join(testDir, 'test.txt');
      await fs.writeFile(testFile, 'test content');
      expect(await fileExists(testFile)).toBe(true);
    });

    it('should return false for non-existing file', async () => {
      const nonExistentFile = path.join(testDir, 'non-existent.txt');
      expect(await fileExists(nonExistentFile)).toBe(false);
    });
  });

  describe('isDirectory', () => {
    it('should return true for directory', async () => {
      expect(await isDirectory(testDir)).toBe(true);
    });

    it('should return false for file', async () => {
      const testFile = path.join(testDir, 'test.txt');
      await fs.writeFile(testFile, 'test content');
      expect(await isDirectory(testFile)).toBe(false);
    });

    it('should return false for non-existent path', async () => {
      expect(await isDirectory('/non/existent/path')).toBe(false);
    });
  });
});

describe('String Utilities', () => {
  describe('sanitizeFilename', () => {
    it('should replace invalid characters with underscores', () => {
      expect(sanitizeFilename('file<name>with:invalid"chars')).toBe('file_name_with_invalid_chars');
    });

    it('should replace spaces with underscores', () => {
      expect(sanitizeFilename('file name with spaces')).toBe('file_name_with_spaces');
    });

    it('should handle empty string', () => {
      expect(sanitizeFilename('')).toBe('');
    });
  });

  describe('truncateString', () => {
    it('should truncate long strings', () => {
      expect(truncateString('This is a very long string', 10)).toBe('This is...');
    });

    it('should not truncate short strings', () => {
      expect(truncateString('Short', 10)).toBe('Short');
    });

    it('should handle exact length', () => {
      expect(truncateString('Exact', 5)).toBe('Exact');
    });
  });

  describe('capitalizeFirst', () => {
    it('should capitalize first letter', () => {
      expect(capitalizeFirst('hello')).toBe('Hello');
    });

    it('should handle empty string', () => {
      expect(capitalizeFirst('')).toBe('');
    });

    it('should not change already capitalized string', () => {
      expect(capitalizeFirst('Hello')).toBe('Hello');
    });
  });

  describe('camelToKebab', () => {
    it('should convert camelCase to kebab-case', () => {
      expect(camelToKebab('camelCaseString')).toBe('camel-case-string');
    });

    it('should handle single word', () => {
      expect(camelToKebab('word')).toBe('word');
    });

    it('should handle numbers', () => {
      expect(camelToKebab('version2String')).toBe('version2-string');
    });
  });

  describe('kebabToCamel', () => {
    it('should convert kebab-case to camelCase', () => {
      expect(kebabToCamel('kebab-case-string')).toBe('kebabCaseString');
    });

    it('should handle single word', () => {
      expect(kebabToCamel('word')).toBe('word');
    });
  });
});

describe('Array Utilities', () => {
  describe('chunk', () => {
    it('should split array into chunks', () => {
      expect(chunk([1, 2, 3, 4, 5], 2)).toEqual([[1, 2], [3, 4], [5]]);
    });

    it('should handle empty array', () => {
      expect(chunk([], 2)).toEqual([]);
    });

    it('should handle chunk size larger than array', () => {
      expect(chunk([1, 2], 5)).toEqual([[1, 2]]);
    });
  });

  describe('unique', () => {
    it('should remove duplicates', () => {
      expect(unique([1, 2, 2, 3, 3, 3])).toEqual([1, 2, 3]);
    });

    it('should handle empty array', () => {
      expect(unique([])).toEqual([]);
    });

    it('should handle array with no duplicates', () => {
      expect(unique([1, 2, 3])).toEqual([1, 2, 3]);
    });
  });

  describe('groupBy', () => {
    it('should group items by key', () => {
      const items = [
        { type: 'fruit', name: 'apple' },
        { type: 'fruit', name: 'banana' },
        { type: 'vegetable', name: 'carrot' },
      ];
      
      const grouped = groupBy(items, item => item.type);
      
      expect(grouped.fruit).toHaveLength(2);
      expect(grouped.vegetable).toHaveLength(1);
    });
  });
});

describe('Object Utilities', () => {
  describe('deepClone', () => {
    it('should clone primitive values', () => {
      expect(deepClone(42)).toBe(42);
      expect(deepClone('string')).toBe('string');
      expect(deepClone(true)).toBe(true);
      expect(deepClone(null)).toBe(null);
    });

    it('should clone arrays', () => {
      const original = [1, 2, { a: 3 }];
      const cloned = deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[2]).not.toBe(original[2]);
    });

    it('should clone objects', () => {
      const original = { a: 1, b: { c: 2 } };
      const cloned = deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
    });

    it('should clone dates', () => {
      const original = new Date();
      const cloned = deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
    });
  });

  describe('mergeDeep', () => {
    it('should merge objects deeply', () => {
      const target = { a: 1, b: { c: 2, d: 3 } };
      const source = { b: { c: 4 }, e: 5 };
      
      const result = mergeDeep(target, source);
      
      expect(result).toEqual({
        a: 1,
        b: { c: 4, d: 3 },
        e: 5,
      });
    });

    it('should not mutate original objects', () => {
      const target = { a: 1, b: { c: 2 } };
      const source = { b: { d: 3 } };
      
      mergeDeep(target, source);
      
      expect(target.b).not.toHaveProperty('d');
    });
  });
});

describe('Time Utilities', () => {
  describe('formatDuration', () => {
    it('should format milliseconds', () => {
      expect(formatDuration(500)).toBe('500ms');
    });

    it('should format seconds', () => {
      expect(formatDuration(1500)).toBe('1.5s');
    });

    it('should format minutes', () => {
      expect(formatDuration(90000)).toBe('1.5m');
    });

    it('should format hours', () => {
      expect(formatDuration(7200000)).toBe('2.0h');
    });
  });

  describe('formatBytes', () => {
    it('should format bytes', () => {
      expect(formatBytes(500)).toBe('500.0 B');
    });

    it('should format kilobytes', () => {
      expect(formatBytes(1536)).toBe('1.5 KB');
    });

    it('should format megabytes', () => {
      expect(formatBytes(1572864)).toBe('1.5 MB');
    });
  });

  describe('sleep', () => {
    it('should wait for specified time', async () => {
      const start = Date.now();
      await sleep(100);
      const elapsed = Date.now() - start;
      
      expect(elapsed).toBeGreaterThanOrEqual(90); // Allow some variance
      expect(elapsed).toBeLessThan(200);
    });
  });
});

describe('Validation Utilities', () => {
  describe('isValidEmail', () => {
    it('should validate correct emails', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid emails', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
    });
  });

  describe('isValidUrl', () => {
    it('should validate correct URLs', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://localhost:3000')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('ftp://')).toBe(false);
    });
  });
});

describe('ID Generation', () => {
  describe('generateId', () => {
    it('should generate ID of specified length', () => {
      expect(generateId(8)).toHaveLength(8);
      expect(generateId(16)).toHaveLength(16);
    });

    it('should generate different IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
    });

    it('should only contain valid characters', () => {
      const id = generateId(100);
      expect(id).toMatch(/^[A-Za-z0-9]+$/);
    });
  });
});
