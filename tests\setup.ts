import { jest } from '@jest/globals';

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env['NODE_ENV'] = 'test';
  
  // Mock console methods to reduce noise in tests
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
  
  // Set longer timeout for integration tests
  jest.setTimeout(30000);
});

afterAll(async () => {
  // Restore console methods
  jest.restoreAllMocks();
});

// Global test utilities
global.testUtils = {
  createMockLogger: () => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  }),
  
  createMockContext: () => ({
    sessionId: 'test-session',
    workingDirectory: '/test/dir',
    projectStructure: {
      root: '/test/dir',
      files: [],
      directories: [],
    },
    environment: {
      platform: 'linux' as const,
      arch: 'x64',
      nodeVersion: 'v18.0.0',
      shell: 'bash',
      cwd: '/test/dir',
      env: {},
    },
    history: [],
    memory: {
      facts: {},
      patterns: [],
      preferences: {},
      learnings: [],
    },
  }),
  
  sleep: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
};

// Extend Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidPath(): R;
      toBeValidEmail(): R;
      toBeValidUrl(): R;
    }
  }
  
  var testUtils: {
    createMockLogger: () => any;
    createMockContext: () => any;
    sleep: (ms: number) => Promise<void>;
  };
}

// Custom matchers
expect.extend({
  toBeValidPath(received: string) {
    const pass = typeof received === 'string' && received.length > 0;
    return {
      message: () => `expected ${received} to be a valid path`,
      pass,
    };
  },
  
  toBeValidEmail(received: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = emailRegex.test(received);
    return {
      message: () => `expected ${received} to be a valid email`,
      pass,
    };
  },
  
  toBeValidUrl(received: string) {
    try {
      new URL(received);
      return {
        message: () => `expected ${received} to be a valid URL`,
        pass: true,
      };
    } catch {
      return {
        message: () => `expected ${received} to be a valid URL`,
        pass: false,
      };
    }
  },
});
