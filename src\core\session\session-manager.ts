import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { nanoid } from 'nanoid';
import { Session, AgentContext, Config } from '@/types';

export class SessionManager extends EventEmitter {
  private readonly logger: Logger;
  private readonly sessionsDir: string;
  private sessions = new Map<string, Session>();

  constructor(logger: Logger, sessionsDir?: string) {
    super();
    this.logger = logger;
    this.sessionsDir = sessionsDir || path.join(process.cwd(), '.agent-sessions');
    this.initializeSessionsDirectory();
  }

  private async initializeSessionsDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.sessionsDir, { recursive: true });
      await this.loadExistingSessions();
    } catch (error) {
      this.logger.error('Failed to initialize sessions directory', { error });
    }
  }

  private async loadExistingSessions(): Promise<void> {
    try {
      const files = await fs.readdir(this.sessionsDir);
      const sessionFiles = files.filter(file => file.endsWith('.json'));

      for (const file of sessionFiles) {
        try {
          const filePath = path.join(this.sessionsDir, file);
          const data = await fs.readFile(filePath, 'utf8');
          const session = JSON.parse(data) as Session;
          
          // Convert date strings back to Date objects
          session.createdAt = new Date(session.createdAt);
          session.lastAccessed = new Date(session.lastAccessed);
          
          this.sessions.set(session.id, session);
        } catch (error) {
          this.logger.warn(`Failed to load session file: ${file}`, { error });
        }
      }

      this.logger.info(`Loaded ${this.sessions.size} existing sessions`);
    } catch (error) {
      this.logger.warn('Failed to load existing sessions', { error });
    }
  }

  async createSession(
    name: string,
    context: AgentContext,
    config: Config
  ): Promise<Session> {
    const session: Session = {
      id: nanoid(),
      name,
      createdAt: new Date(),
      lastAccessed: new Date(),
      context,
      config,
    };

    this.sessions.set(session.id, session);
    await this.saveSession(session);

    this.logger.info('Session created', { sessionId: session.id, name });
    this.emit('sessionCreated', { session });

    return session;
  }

  async getSession(id: string): Promise<Session | undefined> {
    const session = this.sessions.get(id);
    if (session) {
      session.lastAccessed = new Date();
      await this.saveSession(session);
      this.emit('sessionAccessed', { session });
    }
    return session;
  }

  async updateSession(id: string, updates: Partial<Session>): Promise<void> {
    const session = this.sessions.get(id);
    if (!session) {
      throw new Error(`Session not found: ${id}`);
    }

    Object.assign(session, updates);
    session.lastAccessed = new Date();
    
    this.sessions.set(id, session);
    await this.saveSession(session);

    this.logger.info('Session updated', { sessionId: id });
    this.emit('sessionUpdated', { session });
  }

  async deleteSession(id: string): Promise<void> {
    const session = this.sessions.get(id);
    if (!session) {
      throw new Error(`Session not found: ${id}`);
    }

    this.sessions.delete(id);
    
    try {
      const filePath = path.join(this.sessionsDir, `${id}.json`);
      await fs.unlink(filePath);
    } catch (error) {
      this.logger.warn('Failed to delete session file', { error, sessionId: id });
    }

    this.logger.info('Session deleted', { sessionId: id });
    this.emit('sessionDeleted', { sessionId: id });
  }

  async listSessions(): Promise<Session[]> {
    return Array.from(this.sessions.values()).sort(
      (a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime()
    );
  }

  async exportSession(id: string, exportPath: string): Promise<void> {
    const session = this.sessions.get(id);
    if (!session) {
      throw new Error(`Session not found: ${id}`);
    }

    await fs.mkdir(path.dirname(exportPath), { recursive: true });
    await fs.writeFile(exportPath, JSON.stringify(session, null, 2));

    this.logger.info('Session exported', { sessionId: id, exportPath });
    this.emit('sessionExported', { session, exportPath });
  }

  async importSession(importPath: string): Promise<Session> {
    try {
      const data = await fs.readFile(importPath, 'utf8');
      const sessionData = JSON.parse(data);
      
      // Generate new ID to avoid conflicts
      const session: Session = {
        ...sessionData,
        id: nanoid(),
        createdAt: new Date(sessionData.createdAt),
        lastAccessed: new Date(),
      };

      this.sessions.set(session.id, session);
      await this.saveSession(session);

      this.logger.info('Session imported', { sessionId: session.id, importPath });
      this.emit('sessionImported', { session, importPath });

      return session;
    } catch (error) {
      this.logger.error('Failed to import session', { error, importPath });
      throw error;
    }
  }

  async findSessionsByName(name: string): Promise<Session[]> {
    return Array.from(this.sessions.values()).filter(
      session => session.name.toLowerCase().includes(name.toLowerCase())
    );
  }

  async findSessionsByWorkingDirectory(workingDirectory: string): Promise<Session[]> {
    return Array.from(this.sessions.values()).filter(
      session => session.context.workingDirectory === workingDirectory
    );
  }

  async cleanupOldSessions(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<number> {
    const cutoffDate = new Date(Date.now() - maxAge);
    const sessionsToDelete: string[] = [];

    for (const [id, session] of this.sessions.entries()) {
      if (session.lastAccessed < cutoffDate) {
        sessionsToDelete.push(id);
      }
    }

    for (const id of sessionsToDelete) {
      await this.deleteSession(id);
    }

    this.logger.info(`Cleaned up ${sessionsToDelete.length} old sessions`);
    return sessionsToDelete.length;
  }

  private async saveSession(session: Session): Promise<void> {
    try {
      const filePath = path.join(this.sessionsDir, `${session.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(session, null, 2));
    } catch (error) {
      this.logger.error('Failed to save session', { error, sessionId: session.id });
      throw error;
    }
  }

  async getSessionStats(): Promise<{
    totalSessions: number;
    recentSessions: number;
    oldestSession?: Date;
    newestSession?: Date;
  }> {
    const sessions = Array.from(this.sessions.values());
    const recentCutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days

    return {
      totalSessions: sessions.length,
      recentSessions: sessions.filter(s => s.lastAccessed > recentCutoff).length,
      oldestSession: sessions.length > 0 
        ? new Date(Math.min(...sessions.map(s => s.createdAt.getTime())))
        : undefined,
      newestSession: sessions.length > 0
        ? new Date(Math.max(...sessions.map(s => s.createdAt.getTime())))
        : undefined,
    };
  }

  async shutdown(): Promise<void> {
    // Save all sessions before shutdown
    const savePromises = Array.from(this.sessions.values()).map(session => 
      this.saveSession(session).catch(error => 
        this.logger.warn('Failed to save session during shutdown', { error, sessionId: session.id })
      )
    );

    await Promise.allSettled(savePromises);
    this.sessions.clear();
    this.removeAllListeners();
    
    this.logger.info('Session manager shutdown complete');
  }
}
