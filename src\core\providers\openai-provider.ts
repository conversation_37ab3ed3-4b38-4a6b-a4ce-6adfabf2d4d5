import OpenAI from 'openai';
import { <PERSON><PERSON> } from 'winston';
import { <PERSON><PERSON><PERSON><PERSON>, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { ProviderInterface, GenerationOptions } from './llm-provider-manager';

export class OpenAIProvider implements ProviderInterface {
  public readonly name: string;
  public readonly type: <PERSON><PERSON><PERSON>ider['type'] = 'openai';
  
  private client: OpenAI;
  private config: <PERSON><PERSON><PERSON><PERSON>;
  private logger: Logger;

  constructor(config: <PERSON><PERSON><PERSON><PERSON>, logger: Logger) {
    this.name = config.name;
    this.config = config;
    this.logger = logger;

    if (!config.apiKey) {
      throw new ProviderError('OpenAI API key is required', config.name);
    }

    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      this.logger.warn(`OpenAI provider ${this.name} not available`, { error });
      return false;
    }
  }

  async generateResponse(
    messages: LLMMessage[], 
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    try {
      const openaiMessages = this.convertMessages(messages);
      
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: openaiMessages,
        temperature: options.temperature ?? this.config.temperature,
        max_tokens: options.maxTokens ?? this.config.maxTokens,
        ...(options.topP !== undefined && { top_p: options.topP }),
        ...(options.frequencyPenalty !== undefined && { frequency_penalty: options.frequencyPenalty }),
        ...(options.presencePenalty !== undefined && { presence_penalty: options.presencePenalty }),
        ...(options.stop !== undefined && { stop: options.stop }),
        stream: false, // We'll handle streaming separately if needed
        tools: this.getToolDefinitions(),
        tool_choice: 'auto',
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new ProviderError('No response choice returned', this.name);
      }

      const toolCalls = choice.message.tool_calls?.map(call => ({
        id: call.id,
        type: 'function' as const,
        function: {
          name: call.function.name,
          arguments: call.function.arguments,
        },
      }));

      return {
        content: choice.message.content || '',
        toolCalls,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
        finishReason: this.mapFinishReason(choice.finish_reason),
      };
    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        throw new ProviderError(
          `OpenAI API error: ${error.message}`,
          this.name,
          { status: error.status, code: error.code }
        );
      }
      throw new ProviderError(`OpenAI request failed: ${(error as Error).message}`, this.name);
    }
  }

  private convertMessages(messages: LLMMessage[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => {
      switch (msg.role) {
        case 'system':
          return { role: 'system', content: msg.content };
        case 'user':
          return { role: 'user', content: msg.content };
        case 'assistant':
          if (msg.toolCalls) {
            return {
              role: 'assistant',
              content: msg.content,
              tool_calls: msg.toolCalls.map(call => ({
                id: call.id,
                type: 'function',
                function: {
                  name: call.function.name,
                  arguments: call.function.arguments,
                },
              })),
            };
          }
          return { role: 'assistant', content: msg.content };
        case 'tool':
          return {
            role: 'tool',
            content: msg.content,
            tool_call_id: msg.toolCallId!,
          };
        default:
          throw new ProviderError(`Unsupported message role: ${msg.role}`, this.name);
      }
    });
  }

  private getToolDefinitions(): OpenAI.Chat.Completions.ChatCompletionTool[] {
    return [
      {
        type: 'function',
        function: {
          name: 'execute_shell_command',
          description: 'Execute shell commands with full system access',
          parameters: {
            type: 'object',
            properties: {
              command: {
                type: 'string',
                description: 'The shell command to execute',
              },
              workingDirectory: {
                type: 'string',
                description: 'Working directory for command execution',
              },
              timeout: {
                type: 'number',
                description: 'Timeout in milliseconds',
              },
            },
            required: ['command'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'read_file',
          description: 'Read file contents',
          parameters: {
            type: 'object',
            properties: {
              path: {
                type: 'string',
                description: 'File path to read',
              },
              encoding: {
                type: 'string',
                description: 'File encoding (default: utf8)',
              },
            },
            required: ['path'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'write_file',
          description: 'Write content to file',
          parameters: {
            type: 'object',
            properties: {
              path: {
                type: 'string',
                description: 'File path to write',
              },
              content: {
                type: 'string',
                description: 'Content to write',
              },
              encoding: {
                type: 'string',
                description: 'File encoding (default: utf8)',
              },
              createDirectories: {
                type: 'boolean',
                description: 'Create parent directories if they don\'t exist',
              },
            },
            required: ['path', 'content'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'search_files',
          description: 'Search for files using glob patterns',
          parameters: {
            type: 'object',
            properties: {
              pattern: {
                type: 'string',
                description: 'Glob pattern to search for files',
              },
              directory: {
                type: 'string',
                description: 'Directory to search in',
              },
              includeContent: {
                type: 'boolean',
                description: 'Include file content in results',
              },
            },
            required: ['pattern'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'analyze_project',
          description: 'Analyze project structure and dependencies',
          parameters: {
            type: 'object',
            properties: {
              directory: {
                type: 'string',
                description: 'Project directory to analyze',
              },
              includeGitInfo: {
                type: 'boolean',
                description: 'Include git repository information',
              },
            },
            required: ['directory'],
          },
        },
      },
    ];
  }

  private mapFinishReason(reason: string | null): LLMResponse['finishReason'] {
    switch (reason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'tool_calls':
        return 'tool_calls';
      case 'content_filter':
        return 'content_filter';
      default:
        return 'stop';
    }
  }

  estimateTokens(text: string): number {
    // Rough estimation for OpenAI models: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  getMaxTokens(): number {
    // Model-specific max tokens
    const modelLimits: Record<string, number> = {
      'gpt-4': 8192,
      'gpt-4-32k': 32768,
      'gpt-4-turbo': 128000,
      'gpt-4o': 128000,
      'gpt-3.5-turbo': 4096,
      'gpt-3.5-turbo-16k': 16384,
    };

    return modelLimits[this.config.model] || this.config.maxTokens;
  }
}
