import { EventEmitter } from 'events';
import { createInterface, Interface } from 'readline';
import chalk from 'chalk';
import ora, { Ora } from 'ora';
import { Logger } from 'winston';
import { nanoid } from 'nanoid';

import { ConfigManager } from '@config/config-manager';
import { AutonomousAgent } from '@core/agent/autonomous-agent';
import { LLMProviderManager } from '@core/providers/llm-provider-manager';
import { ToolRegistry } from '@tools/tool-registry';
import { ContextManager } from '@core/context/context-manager';
import { TerminalUI } from './terminal-ui';
import { ProgressManager } from './progress-manager';
import { AgentContext, ToolResult } from '@/types';

export interface StartOptions {
  workingDirectory: string;
  sessionId?: string;
}

export interface ExecuteOptions {
  workingDirectory: string;
  timeout?: number;
}

export class AgentCLI extends EventEmitter {
  private readonly logger: Logger;
  private readonly configManager: ConfigManager;
  private readonly providerManager: LLMProviderManager;
  private readonly toolRegistry: ToolRegistry;
  private readonly contextManager: ContextManager;
  private readonly terminalUI: TerminalUI;
  private readonly progressManager: ProgressManager;
  
  private agent?: AutonomousAgent;
  private readline?: Interface;
  private currentContext?: AgentContext;
  private isRunning = false;
  private spinner?: Ora;

  constructor(logger: Logger, configManager: ConfigManager) {
    super();
    
    this.logger = logger;
    this.configManager = configManager;
    this.providerManager = new LLMProviderManager(logger);
    this.toolRegistry = new ToolRegistry(logger);
    this.contextManager = new ContextManager(logger);
    this.terminalUI = new TerminalUI(logger, configManager);
    this.progressManager = new ProgressManager(logger);
    
    this.setupEventHandlers();
  }

  async start(options: StartOptions): Promise<void> {
    this.logger.info('Starting Agent CLI', options);

    try {
      // Initialize components
      await this.initialize(options);
      
      // Show welcome message
      this.terminalUI.showWelcome();
      
      // Start interactive session
      await this.startInteractiveSession();
      
    } catch (error) {
      this.logger.error('Failed to start Agent CLI', { error });
      throw error;
    }
  }

  async executeGoal(goal: string, options: ExecuteOptions): Promise<ToolResult> {
    this.logger.info('Executing goal', { goal, options });

    try {
      // Initialize for single execution
      await this.initialize({
        workingDirectory: options.workingDirectory,
        sessionId: nanoid(),
      });

      if (!this.agent || !this.currentContext) {
        throw new Error('Agent not properly initialized');
      }

      // Execute goal with timeout
      const timeoutPromise = options.timeout 
        ? new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Execution timeout')), options.timeout)
          )
        : new Promise<never>(() => {}); // Never resolves if no timeout

      const executionPromise = this.agent.executeGoal(goal, this.currentContext);

      await Promise.race([executionPromise, timeoutPromise]);

      return {
        success: true,
        data: 'Goal completed successfully',
      };
    } catch (error) {
      this.logger.error('Goal execution failed', { error, goal });
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  private async initialize(options: StartOptions): Promise<void> {
    this.logger.info('Initializing Agent CLI components');

    // Initialize LLM providers
    const providers = this.configManager.getProviders();
    await this.providerManager.initialize(providers);

    // Configure tools
    const toolsConfig = this.configManager.getToolsConfig();
    this.toolRegistry.setDangerousToolsEnabled(toolsConfig.allowDangerous);

    // Initialize context
    this.currentContext = await this.contextManager.initializeContext(
      options.workingDirectory,
      options.sessionId
    );

    // Create autonomous agent
    this.agent = new AutonomousAgent(
      this.logger,
      this.providerManager,
      this.toolRegistry,
      this.contextManager,
      toolsConfig.maxParallel
    );

    // Configure session settings
    const sessionConfig = this.configManager.getSessionConfig();
    this.contextManager.setAutoSave(sessionConfig.autoSave);

    this.logger.info('Agent CLI initialized successfully');
  }

  private async startInteractiveSession(): Promise<void> {
    this.readline = createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: chalk.cyan('agent> '),
    });

    this.readline.prompt();

    this.readline.on('line', async (input) => {
      const trimmedInput = input.trim();
      
      if (!trimmedInput) {
        this.readline!.prompt();
        return;
      }

      await this.handleUserInput(trimmedInput);
      this.readline!.prompt();
    });

    this.readline.on('close', () => {
      this.terminalUI.showGoodbye();
      this.shutdown();
    });

    // Handle Ctrl+C gracefully
    this.readline.on('SIGINT', () => {
      if (this.isRunning) {
        console.log(chalk.yellow('\nStopping current operation...'));
        this.agent?.stop();
      } else {
        console.log(chalk.yellow('\nGoodbye!'));
        this.readline?.close();
      }
    });
  }

  private async handleUserInput(input: string): Promise<void> {
    // Handle special commands
    if (input.startsWith('/')) {
      await this.handleCommand(input);
      return;
    }

    // Handle regular goals
    if (!this.agent || !this.currentContext) {
      console.log(chalk.red('Agent not initialized. Please restart the CLI.'));
      return;
    }

    if (this.isRunning) {
      console.log(chalk.yellow('Agent is currently busy. Please wait or use /stop to cancel.'));
      return;
    }

    try {
      this.isRunning = true;
      this.spinner = ora('Analyzing goal and creating plan...').start();
      
      await this.agent.executeGoal(input, this.currentContext);
      
      this.spinner.succeed('Goal completed successfully!');
    } catch (error) {
      this.spinner?.fail('Goal execution failed');
      console.log(chalk.red('Error:'), (error as Error).message);
    } finally {
      this.isRunning = false;
      this.spinner = undefined;
    }
  }

  private async handleCommand(command: string): Promise<void> {
    const [cmd, ...args] = command.slice(1).split(' ');

    switch (cmd) {
      case 'help':
        this.terminalUI.showHelp();
        break;

      case 'status':
        this.showStatus();
        break;

      case 'context':
        this.showContext();
        break;

      case 'providers':
        this.showProviders();
        break;

      case 'tools':
        this.showTools();
        break;

      case 'config':
        this.showConfig();
        break;

      case 'history':
        this.showHistory();
        break;

      case 'clear':
        console.clear();
        this.terminalUI.showWelcome();
        break;

      case 'save':
        await this.saveSession(args[0]);
        break;

      case 'load':
        await this.loadSession(args[0]);
        break;

      case 'stop':
        if (this.isRunning) {
          this.agent?.stop();
          this.spinner?.stop();
          console.log(chalk.yellow('Operation stopped'));
        } else {
          console.log(chalk.yellow('No operation running'));
        }
        break;

      case 'exit':
      case 'quit':
        this.readline?.close();
        break;

      default:
        console.log(chalk.red(`Unknown command: ${cmd}`));
        console.log(chalk.gray('Type /help for available commands'));
    }
  }

  private showStatus(): void {
    console.log(chalk.blue('\n=== Agent Status ==='));
    console.log(`Running: ${this.isRunning ? chalk.green('Yes') : chalk.gray('No')}`);
    console.log(`Session ID: ${this.currentContext?.sessionId || 'None'}`);
    console.log(`Working Directory: ${this.currentContext?.workingDirectory || 'None'}`);
    console.log(`Current Provider: ${this.providerManager.getCurrentProvider()?.name || 'None'}`);
    console.log(`Tools Available: ${this.toolRegistry.getAvailableTools().length}`);
    console.log(`Dangerous Tools: ${this.toolRegistry.isDangerousToolsEnabled() ? chalk.red('Enabled') : chalk.green('Disabled')}`);
  }

  private showContext(): void {
    if (!this.currentContext) {
      console.log(chalk.yellow('No context available'));
      return;
    }

    console.log(chalk.blue('\n=== Context Information ==='));
    console.log(`Files: ${this.currentContext.projectStructure.files.length}`);
    console.log(`Directories: ${this.currentContext.projectStructure.directories.length}`);
    console.log(`Platform: ${this.currentContext.environment.platform}`);
    console.log(`Node Version: ${this.currentContext.environment.nodeVersion}`);
    console.log(`Actions in History: ${this.currentContext.history.length}`);
    
    if (this.currentContext.projectStructure.packageInfo) {
      const pkg = this.currentContext.projectStructure.packageInfo;
      console.log(`Project: ${pkg.name} (${pkg.type})`);
      console.log(`Dependencies: ${Object.keys(pkg.dependencies).length}`);
    }
  }

  private async showProviders(): Promise<void> {
    console.log(chalk.blue('\n=== LLM Providers ==='));
    
    const providers = this.configManager.getProviders();
    const health = await this.providerManager.healthCheck();
    
    providers.forEach(provider => {
      const status = health[provider.name] ? chalk.green('✓') : chalk.red('✗');
      const current = this.providerManager.getCurrentProvider()?.name === provider.name ? chalk.yellow(' (current)') : '';
      console.log(`${status} ${provider.name} (${provider.type}) - ${provider.model}${current}`);
    });
  }

  private showTools(): void {
    console.log(chalk.blue('\n=== Available Tools ==='));
    
    const tools = this.toolRegistry.getAvailableTools();
    tools.forEach(tool => {
      const dangerous = tool.dangerous ? chalk.red(' [DANGEROUS]') : '';
      const parallel = tool.parallel ? chalk.green(' [PARALLEL]') : '';
      console.log(`• ${tool.name}${dangerous}${parallel}`);
      console.log(`  ${chalk.gray(tool.description)}`);
    });
  }

  private showConfig(): void {
    console.log(chalk.blue('\n=== Configuration ==='));
    const config = this.configManager.getConfig();
    console.log(JSON.stringify(config, null, 2));
  }

  private showHistory(): void {
    if (!this.currentContext) {
      console.log(chalk.yellow('No context available'));
      return;
    }

    console.log(chalk.blue('\n=== Action History ==='));
    const history = this.currentContext.history.slice(-10); // Show last 10 actions
    
    if (history.length === 0) {
      console.log(chalk.gray('No actions in history'));
      return;
    }

    history.forEach((action, index) => {
      const status = action.success ? chalk.green('✓') : chalk.red('✗');
      const time = new Date(action.timestamp).toLocaleTimeString();
      console.log(`${status} [${time}] ${action.type} (${action.duration}ms)`);
    });
  }

  private async saveSession(name?: string): Promise<void> {
    try {
      const sessionName = name || `session-${Date.now()}`;
      await this.contextManager.saveContext(`./sessions/${sessionName}.json`);
      console.log(chalk.green(`Session saved as: ${sessionName}`));
    } catch (error) {
      console.log(chalk.red('Failed to save session:'), (error as Error).message);
    }
  }

  private async loadSession(name: string): Promise<void> {
    try {
      await this.contextManager.loadContext(`./sessions/${name}.json`);
      console.log(chalk.green(`Session loaded: ${name}`));
    } catch (error) {
      console.log(chalk.red('Failed to load session:'), (error as Error).message);
    }
  }

  private setupEventHandlers(): void {
    // Agent events
    this.agent?.on('started', ({ goal }) => {
      this.spinner?.text = `Executing: ${goal}`;
    });

    this.agent?.on('planCreated', ({ plan }) => {
      this.spinner?.text = `Plan created with ${plan.tasks.length} tasks`;
    });

    this.agent?.on('taskStarted', ({ task }) => {
      this.spinner?.text = `Executing: ${task.description}`;
    });

    this.agent?.on('taskCompleted', ({ task }) => {
      this.logger.debug('Task completed', { taskId: task.id });
    });

    this.agent?.on('error', ({ error }) => {
      this.spinner?.fail('Agent error occurred');
      console.log(chalk.red('Agent Error:'), error.message);
    });

    // Context events
    this.contextManager.on('contextSaved', ({ path }) => {
      this.logger.debug('Context saved', { path });
    });

    // Provider events
    this.providerManager.on('providerSwitched', ({ from, to }) => {
      console.log(chalk.yellow(`Switched provider: ${from} → ${to}`));
    });
  }

  private async shutdown(): Promise<void> {
    this.logger.info('Shutting down Agent CLI');

    try {
      if (this.agent) {
        this.agent.stop();
      }

      await this.contextManager.shutdown();
      await this.providerManager.shutdown();
      await this.toolRegistry.shutdown();

      this.readline?.close();
    } catch (error) {
      this.logger.error('Error during shutdown', { error });
    }

    process.exit(0);
  }
}
