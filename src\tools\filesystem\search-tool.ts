import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { z } from 'zod';
import glob from 'fast-glob';
import { BaseTool, ToolResult, AgentContext } from '@/types';

const SearchToolSchema = z.object({
  pattern: z.string().min(1, 'Search pattern cannot be empty'),
  directory: z.string().optional(),
  includeContent: z.boolean().default(false),
  maxResults: z.number().min(1).max(1000).default(100),
  fileTypes: z.array(z.string()).optional(),
  excludePatterns: z.array(z.string()).default([
    '**/node_modules/**',
    '**/.git/**',
    '**/dist/**',
    '**/build/**',
    '**/.vscode/**',
    '**/.idea/**',
    '**/coverage/**',
    '**/__pycache__/**',
    '**/target/**',
  ]),
  caseSensitive: z.boolean().default(false),
  searchContent: z.boolean().default(false),
  contentPattern: z.string().optional(),
  maxFileSize: z.number().default(1024 * 1024), // 1MB default
});

type SearchToolParams = z.infer<typeof SearchToolSchema>;

interface SearchResult {
  path: string;
  name: string;
  size: number;
  lastModified: Date;
  type: 'file' | 'directory';
  content?: string;
  matches?: Array<{
    line: number;
    text: string;
    column: number;
  }>;
}

export class SearchTool extends BaseTool<SearchToolParams> {
  public readonly name = 'search_files';
  public readonly description = 'Search for files using glob patterns and content search with grep-like functionality';
  public readonly parameters = SearchToolSchema;
  public readonly parallel = true;
  public readonly dangerous = false;

  private readonly logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  protected async executeTyped(params: SearchToolParams, context: AgentContext): Promise<ToolResult> {
    this.logger.info('Executing file search', { 
      pattern: params.pattern,
      directory: params.directory || context.workingDirectory,
      searchContent: params.searchContent 
    });

    try {
      const searchDir = params.directory || context.workingDirectory;
      const results: SearchResult[] = [];

      // Build glob pattern
      const globPattern = this.buildGlobPattern(params.pattern, params.fileTypes);
      
      // Search for files
      const files = await glob(globPattern, {
        cwd: searchDir,
        ignore: params.excludePatterns,
        absolute: true,
        caseSensitiveMatch: params.caseSensitive,
        onlyFiles: true,
        stats: true,
      });

      // Process found files
      for (const file of files.slice(0, params.maxResults)) {
        try {
          const stats = await fs.stat(file.path);
          
          // Skip large files unless explicitly requested
          if (stats.size > params.maxFileSize && !params.includeContent) {
            continue;
          }

          const result: SearchResult = {
            path: file.path,
            name: path.basename(file.path),
            size: stats.size,
            lastModified: stats.mtime,
            type: stats.isDirectory() ? 'directory' : 'file',
          };

          // Include content if requested
          if (params.includeContent && stats.isFile() && stats.size <= params.maxFileSize) {
            try {
              result.content = await fs.readFile(file.path, 'utf8');
            } catch (error) {
              this.logger.warn('Failed to read file content', { path: file.path, error });
            }
          }

          // Search content if pattern provided
          if (params.searchContent && params.contentPattern && stats.isFile()) {
            const matches = await this.searchFileContent(file.path, params.contentPattern, params.caseSensitive);
            if (matches.length > 0) {
              result.matches = matches;
            } else {
              continue; // Skip files without content matches
            }
          }

          results.push(result);
        } catch (error) {
          this.logger.warn('Failed to process file', { path: file.path, error });
        }
      }

      return {
        success: true,
        data: {
          results,
          totalFound: results.length,
          searchPattern: params.pattern,
          searchDirectory: searchDir,
          searchContent: params.searchContent,
          contentPattern: params.contentPattern,
        },
        metadata: {
          searchTime: Date.now(),
          maxResults: params.maxResults,
          includeContent: params.includeContent,
        },
      };
    } catch (error) {
      this.logger.error('File search failed', { error, pattern: params.pattern });

      return {
        success: false,
        error: (error as Error).message,
        metadata: {
          pattern: params.pattern,
          directory: params.directory || context.workingDirectory,
        },
      };
    }
  }

  private buildGlobPattern(pattern: string, fileTypes?: string[]): string {
    let globPattern = pattern;

    // If pattern doesn't contain glob characters, make it a wildcard search
    if (!pattern.includes('*') && !pattern.includes('?') && !pattern.includes('[')) {
      globPattern = `**/*${pattern}*`;
    }

    // Add file type restrictions if specified
    if (fileTypes && fileTypes.length > 0) {
      const extensions = fileTypes.map(type => type.startsWith('.') ? type : `.${type}`);
      if (extensions.length === 1) {
        globPattern += extensions[0];
      } else {
        globPattern += `{${extensions.join(',')}}`;
      }
    }

    return globPattern;
  }

  private async searchFileContent(
    filePath: string, 
    contentPattern: string, 
    caseSensitive: boolean
  ): Promise<Array<{ line: number; text: string; column: number }>> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const lines = content.split('\n');
      const matches: Array<{ line: number; text: string; column: number }> = [];

      const regex = new RegExp(contentPattern, caseSensitive ? 'g' : 'gi');

      lines.forEach((line, index) => {
        let match;
        while ((match = regex.exec(line)) !== null) {
          matches.push({
            line: index + 1,
            text: line.trim(),
            column: match.index + 1,
          });
        }
      });

      return matches;
    } catch (error) {
      this.logger.warn('Failed to search file content', { path: filePath, error });
      return [];
    }
  }

  // Utility methods for common search operations
  async findByExtension(extension: string, context: AgentContext): Promise<ToolResult> {
    return this.execute({
      pattern: `**/*.${extension}`,
      directory: context.workingDirectory,
      includeContent: false,
      maxResults: 100,
      excludePatterns: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/__pycache__/**',
        '**/target/**',
      ],
      caseSensitive: false,
      searchContent: false,
      maxFileSize: 1024 * 1024,
    }, context);
  }

  async findByName(name: string, context: AgentContext): Promise<ToolResult> {
    return this.execute({
      pattern: `**/*${name}*`,
      directory: context.workingDirectory,
      includeContent: false,
      maxResults: 50,
      excludePatterns: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/__pycache__/**',
        '**/target/**',
      ],
      caseSensitive: false,
      searchContent: false,
      maxFileSize: 1024 * 1024,
    }, context);
  }

  async grepFiles(contentPattern: string, filePattern: string, context: AgentContext): Promise<ToolResult> {
    return this.execute({
      pattern: filePattern,
      directory: context.workingDirectory,
      searchContent: true,
      contentPattern,
      includeContent: false,
      maxResults: 100,
      excludePatterns: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/__pycache__/**',
        '**/target/**',
      ],
      caseSensitive: false,
      maxFileSize: 1024 * 1024,
    }, context);
  }

  async findLargeFiles(minSizeMB: number, context: AgentContext): Promise<ToolResult> {
    const result = await this.execute({
      pattern: '**/*',
      directory: context.workingDirectory,
      includeContent: false,
      maxResults: 100,
      maxFileSize: Number.MAX_SAFE_INTEGER,
      excludePatterns: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/__pycache__/**',
        '**/target/**',
      ],
      caseSensitive: false,
      searchContent: false,
    }, context);

    if (result.success && result.data) {
      const minSizeBytes = minSizeMB * 1024 * 1024;
      const largeFiles = (result.data as any).results.filter(
        (file: SearchResult) => file.size > minSizeBytes
      );

      return {
        success: true,
        data: {
          results: largeFiles,
          totalFound: largeFiles.length,
          minSizeMB,
        },
      };
    }

    return result;
  }

  async findRecentFiles(hours: number, context: AgentContext): Promise<ToolResult> {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

    const result = await this.execute({
      pattern: '**/*',
      directory: context.workingDirectory,
      includeContent: false,
      maxResults: 100,
      excludePatterns: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/.vscode/**',
        '**/.idea/**',
        '**/coverage/**',
        '**/__pycache__/**',
        '**/target/**',
      ],
      caseSensitive: false,
      searchContent: false,
      maxFileSize: 1024 * 1024,
    }, context);

    if (result.success && result.data) {
      const recentFiles = (result.data as any).results.filter(
        (file: SearchResult) => new Date(file.lastModified) > cutoffTime
      );

      return {
        success: true,
        data: {
          results: recentFiles,
          totalFound: recentFiles.length,
          cutoffTime,
          hours,
        },
      };
    }

    return result;
  }

  async findEmptyFiles(context: AgentContext): Promise<ToolResult> {
    const result = await this.execute({
      pattern: '**/*',
      directory: context.workingDirectory,
      includeContent: false,
      maxResults: 100,
    }, context);

    if (result.success && result.data) {
      const emptyFiles = (result.data as any).results.filter(
        (file: SearchResult) => file.size === 0
      );

      return {
        success: true,
        data: {
          results: emptyFiles,
          totalFound: emptyFiles.length,
        },
      };
    }

    return result;
  }

  async findDuplicateNames(context: AgentContext): Promise<ToolResult> {
    const result = await this.execute({
      pattern: '**/*',
      directory: context.workingDirectory,
      includeContent: false,
      maxResults: 1000,
    }, context);

    if (result.success && result.data) {
      const files = (result.data as any).results as SearchResult[];
      const nameGroups = new Map<string, SearchResult[]>();

      // Group files by name
      files.forEach(file => {
        const name = file.name.toLowerCase();
        if (!nameGroups.has(name)) {
          nameGroups.set(name, []);
        }
        nameGroups.get(name)!.push(file);
      });

      // Find duplicates
      const duplicates = Array.from(nameGroups.entries())
        .filter(([, group]) => group.length > 1)
        .map(([name, group]) => ({ name, files: group }));

      return {
        success: true,
        data: {
          duplicates,
          totalDuplicateGroups: duplicates.length,
        },
      };
    }

    return result;
  }
}
